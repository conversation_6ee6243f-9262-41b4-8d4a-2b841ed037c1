检测到A100 GPU，应用A100优化设置
使用A100 GPU设备: cuda:0 (NVIDIA A100-PCIE-40GB)
✓ GPU设备测试成功: cuda:0
Namespace(model='qwen3', model_name_or_path='/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B', result_file='/home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/result/few_shot_train_a100_ultra_fast.txt', multi_label=0, multi_verb=1, constraint_loss=1, constraint_alpha=0.1, lm_training=1, lm_alpha=0.5, lr=2e-05, lr2=0.00015, warmup_ratio=0.1, weight_decay=0.01, contrastive_loss=1, contrastive_alpha=1.0, contrastive_level=1, batch_size=4, eval_batch_size=4, depth=7, multi_mask=1, dropout=0.1, shuffle=False, contrastive_logits=1, cs_mode=0, dataset='wos', eval_mode=0, use_hier_mean=1, freeze_plm=0, use_scheduler1=1, use_scheduler2=1, imbalanced_weight=False, imbalanced_weight_reverse=False, device=0, max_grad_norm=1.0, max_seq_lens=512, use_new_ct=1, use_dropout_sim=1, use_withoutWrappedLM=False, mean_verbalizer=False, shot=30, label_description=0, seed=171, plm_eval_mode=False, verbalizer='soft', template_id=0, not_manual=False, gradient_accumulation_steps=1, max_epochs=1, early_stop=10, eval_full=0, use_soft_template=False, use_label_description=False, generate_descriptions=False, use_amp=1, amp_opt_level='O1', use_fp16=0, use_bf16=1, force_bf16=1)
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 5924 ------------
length dataset['train']: 5924

  0%|          | 0/5924 [00:00<?, ?it/s]
100%|██████████| 5924/5924 [00:00<00:00, 3287082.54it/s]

  0%|          | 0/967 [00:00<?, ?it/s]
100%|██████████| 967/967 [00:00<00:00, 3618101.67it/s]

  0%|          | 0/897 [00:00<?, ?it/s]
100%|██████████| 897/897 [00:00<00:00, 1409097.64it/s]
08/02/2025 09:15:30 - INFO - root -   Global seed set to 171
08/02/2025 09:15:30 - INFO - root -   using template: It was 1 level: {"mask"} 2 level: {"mask"} 3 level: {"mask"} 4 level: {"mask"} 5 level: {"mask"} 6 level: {"mask"} 7 level: {"mask"}. {"placeholder": "text_a"}
final train_data length is: 5924
final dev_data length is: 967
final test_data length is: 897
Loading Qwen3 model from /home/<USER>/ZhouSQ/DCX/TACL_chinese1_bert_to_qwen_v2/Qwen3-0.6B
检测到A100 GPU: NVIDIA A100-PCIE-40GB，使用A100优化配置
使用BF16精度训练 (A100原生支持)
目标精度: torch.bfloat16
检测到A100 GPU: NVIDIA A100-PCIE-40GB
A100原生支持BF16，建议使用BF16精度以获得最佳性能
Using manual template (fallback mode)
Using manual template (fallback mode)
train_size: 5924
A100优化：使用8个数据加载工作进程

tokenizing: 0it [00:00, ?it/s]
tokenizing: 273it [00:00, 2721.17it/s]
tokenizing: 552it [00:00, 2759.85it/s]
tokenizing: 830it [00:00, 2767.94it/s]
tokenizing: 1109it [00:00, 2776.27it/s]
tokenizing: 1388it [00:00, 2778.45it/s]
tokenizing: 1666it [00:00, 2762.15it/s]
tokenizing: 1944it [00:00, 2765.07it/s]
tokenizing: 2221it [00:00, 2752.46it/s]
tokenizing: 2497it [00:00, 2746.31it/s]
tokenizing: 2772it [00:01, 2745.36it/s]
tokenizing: 3047it [00:01, 2734.05it/s]
tokenizing: 3324it [00:01, 2744.21it/s]
tokenizing: 3599it [00:01, 2732.19it/s]
tokenizing: 3874it [00:01, 2735.61it/s]
tokenizing: 4151it [00:01, 2743.63it/s]
tokenizing: 4426it [00:01, 2737.34it/s]
tokenizing: 4702it [00:01, 2741.76it/s]
tokenizing: 4978it [00:01, 2745.01it/s]
tokenizing: 5255it [00:01, 2752.42it/s]
tokenizing: 5533it [00:02, 2758.76it/s]
tokenizing: 5809it [00:02, 2752.00it/s]
tokenizing: 5924it [00:02, 2750.53it/s]
08/02/2025 09:15:34 - INFO - root -   The LM head named lm_head was retrieved.
08/02/2025 09:15:34 - INFO - root -   The LM head named lm_head was retrieved.
08/02/2025 09:15:34 - INFO - root -   The LM head named lm_head was retrieved.
08/02/2025 09:15:34 - INFO - root -   The LM head named lm_head was retrieved.
08/02/2025 09:15:34 - INFO - root -   The LM head named lm_head was retrieved.
08/02/2025 09:15:34 - INFO - root -   The LM head named lm_head was retrieved.
08/02/2025 09:15:34 - INFO - root -   The LM head named lm_head was retrieved.
start convert_features_to_dataset
loading prompt model
Creating hierarchical label similarity matrix
Created hierarchical similarity matrix: torch.Size([870, 870])
Model dtype: torch.bfloat16
freeze_plm: 0
plm_eval_mode: False
Trainable parameters: 598,548,480 / 598,548,480 (100.00%)
Ensuring PLM parameters are trainable...
Set verbalizer0 head to dtype: torch.bfloat16
Set verbalizer1 head to dtype: torch.bfloat16
Set verbalizer2 head to dtype: torch.bfloat16
Set verbalizer3 head to dtype: torch.bfloat16
Set verbalizer4 head to dtype: torch.bfloat16
Set verbalizer5 head to dtype: torch.bfloat16
Set verbalizer6 head to dtype: torch.bfloat16
Total optimization steps: 1481 (dataloader_len=1481, accumulation_steps=1, epochs=1)
Warmup steps: 148 (ratio: 0.1)
✓ Scheduler1 (PLM) initialized with warmup
✓ Scheduler2 (Verbalizer) initialized with warmup
saved_path: 2025-08-02_09-15-39-lr-2e-05-lm_training-1-lm_alpha-0.5-batch_size-4
------------ epoch 1 ------------
cur lr	scheduler1: [0.0, 0.0]	scheduler2: [0.0, 0.0]

  0%|          | 0/1481 [00:00<?, ?it/s]
  0%|          | 1/1481 [00:14<6:07:06, 14.88s/it]
  0%|          | 2/1481 [00:29<6:03:39, 14.75s/it]
  0%|          | 3/1481 [00:44<6:01:22, 14.67s/it]
  0%|          | 4/1481 [00:58<5:56:50, 14.50s/it]
  0%|          | 5/1481 [01:12<5:57:20, 14.53s/it]
  0%|          | 6/1481 [01:27<5:54:48, 14.43s/it]
  0%|          | 7/1481 [01:41<5:55:46, 14.48s/it]
  1%|          | 8/1481 [01:49<5:00:07, 12.23s/it]
  1%|          | 9/1481 [02:02<5:12:14, 12.73s/it]
  1%|          | 10/1481 [02:17<5:23:27, 13.19s/it]
  1%|          | 11/1481 [02:31<5:33:40, 13.62s/it]
  1%|          | 12/1481 [02:46<5:37:55, 13.80s/it]
  1%|          | 13/1481 [03:00<5:43:34, 14.04s/it]
  1%|          | 14/1481 [03:14<5:44:35, 14.09s/it]
  1%|          | 15/1481 [03:22<4:57:15, 12.17s/it]
  1%|          | 16/1481 [03:36<5:12:19, 12.79s/it]
  1%|          | 17/1481 [03:51<5:25:33, 13.34s/it]
  1%|          | 18/1481 [04:05<5:32:04, 13.62s/it]
  1%|▏         | 19/1481 [04:20<5:38:58, 13.91s/it]
  1%|▏         | 20/1481 [04:34<5:41:16, 14.02s/it]
  1%|▏         | 21/1481 [04:49<5:45:18, 14.19s/it]
  1%|▏         | 22/1481 [05:03<5:45:27, 14.21s/it]
  2%|▏         | 23/1481 [05:17<5:48:07, 14.33s/it]
  2%|▏         | 24/1481 [05:32<5:49:40, 14.40s/it]
  2%|▏         | 25/1481 [05:47<5:50:46, 14.45s/it]
  2%|▏         | 26/1481 [06:01<5:49:00, 14.39s/it]
  2%|▏         | 27/1481 [06:15<5:50:07, 14.45s/it]
  2%|▏         | 28/1481 [06:30<5:48:33, 14.39s/it]
  2%|▏         | 29/1481 [06:44<5:50:17, 14.47s/it]
  2%|▏         | 30/1481 [06:59<5:48:40, 14.42s/it]
  2%|▏         | 31/1481 [07:13<5:49:42, 14.47s/it]
  2%|▏         | 32/1481 [07:28<5:47:58, 14.41s/it]
  2%|▏         | 33/1481 [07:42<5:49:09, 14.47s/it]
  2%|▏         | 34/1481 [07:43<4:11:47, 10.44s/it]
  2%|▏         | 35/1481 [07:46<3:16:59,  8.17s/it]
  2%|▏         | 36/1481 [08:00<4:00:39,  9.99s/it]
  2%|▏         | 37/1481 [08:15<4:33:21, 11.36s/it]
  3%|▎         | 38/1481 [08:29<4:53:55, 12.22s/it]
  3%|▎         | 39/1481 [08:44<5:11:08, 12.95s/it]
  3%|▎         | 40/1481 [08:58<5:20:09, 13.33s/it]
  3%|▎         | 41/1481 [09:13<5:29:05, 13.71s/it]
  3%|▎         | 42/1481 [09:27<5:32:31, 13.86s/it]
  3%|▎         | 43/1481 [09:41<5:37:29, 14.08s/it]
  3%|▎         | 44/1481 [09:56<5:38:19, 14.13s/it]
  3%|▎         | 45/1481 [10:10<5:41:22, 14.26s/it]
  3%|▎         | 46/1481 [10:18<4:57:51, 12.45s/it]
  3%|▎         | 47/1481 [10:33<5:12:44, 13.09s/it]
  3%|▎         | 48/1481 [10:47<5:21:05, 13.44s/it]
  3%|▎         | 49/1481 [11:02<5:29:02, 13.79s/it]
  3%|▎         | 50/1481 [11:16<5:32:09, 13.93s/it]
  3%|▎         | 51/1481 [11:31<5:36:41, 14.13s/it]
  4%|▎         | 52/1481 [11:42<5:18:13, 13.36s/it]
  4%|▎         | 53/1481 [11:45<4:03:06, 10.21s/it]
  4%|▎         | 54/1481 [11:59<4:31:29, 11.41s/it]
  4%|▎         | 55/1481 [12:14<4:53:13, 12.34s/it]
  4%|▍         | 56/1481 [12:28<5:06:26, 12.90s/it]
  4%|▍         | 57/1481 [12:43<5:17:46, 13.39s/it]
  4%|▍         | 58/1481 [12:49<4:30:49, 11.42s/it]
  4%|▍         | 59/1481 [12:52<3:29:27,  8.84s/it]
  4%|▍         | 60/1481 [13:07<4:08:07, 10.48s/it]